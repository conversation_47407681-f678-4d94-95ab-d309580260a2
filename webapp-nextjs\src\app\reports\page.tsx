'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { reportsApi } from '@/lib/api'
import { ReportAvanzamento, ReportBOQ } from '@/types'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Target,
  Activity,
  Clock,
  CheckCircle,
  Loader2,
  AlertCircle,
  FileText,
  Package,
  Users,
  Zap,
  RefreshCw
} from 'lucide-react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('avanzamento')
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)
  const [reportBOQ, setReportBOQ] = useState<any>(null)
  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)
  const [reportProgress, setReportProgress] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica i report dal backend
  useEffect(() => {
    loadReports()
  }, [cantiere?.id_cantiere])

  const loadReports = async () => {
    try {
      setIsLoading(true)
      setError('')

      const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      // Carica tutti i report disponibili
      const [progressData, boqData, utilizzoData] = await Promise.all([
        reportsApi.getReportProgress(cantiereId).then(res => res.data).catch(() => null),
        reportsApi.getReportBOQ(cantiereId).then(res => res.data).catch(() => null),
        reportsApi.getReportUtilizzoBobine(cantiereId).then(res => res.data).catch(() => null)
      ])

      setReportProgress(progressData)
      setReportBOQ(boqData)
      setReportUtilizzoBobine(utilizzoData)

    } catch (error: any) {
      console.error('Errore caricamento report:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei report')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = () => {
    loadReports()
  }

  const handleExportReport = async (reportType: string, format: string = 'pdf') => {
    try {
      const cantiereId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      if (!cantiereId) return

      let response
      switch (reportType) {
        case 'progress':
          response = await reportsApi.getReportProgress(cantiereId)
          break
        case 'boq':
          response = await reportsApi.getReportBOQ(cantiereId)
          break
        case 'utilizzo-bobine':
          response = await reportsApi.getReportUtilizzoBobine(cantiereId)
          break
        default:
          return
      }

      if (response.data.file_url) {
        // Apri il file in una nuova finestra
        window.open(response.data.file_url, '_blank')
      }
    } catch (error) {
      console.error('Errore export report:', error)
    }
  }

  // Calcolo IAP (Indice di Avanzamento Ponderato)
  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
    const Wp = 2.0  // Peso fase Posa
    const Wc = 1.5  // Peso fase Collegamento
    const Wz = 0.5  // Peso fase Certificazione

    if (nTot === 0) return 0

    const sforzoSoloInstallati = (nInst - nColl) * Wp
    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
    const sforzoCertificati = nCert * (Wp + Wc + Wz)
    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

    const denominatore = nTot * (Wp + Wc + Wz)
    return Math.round((numeratore / denominatore) * 10000) / 100
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Reports</h1>
            <p className="text-slate-600 mt-1">
              {cantiere?.commessa ? `Cantiere: ${cantiere.commessa}` : 'Analisi e statistiche del progetto'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Aggiorna
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Caricamento report...</span>
            </div>
          </div>
        ) : error ? (
          <div className="p-6 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800 font-medium">Errore caricamento report</span>
            </div>
            <p className="text-red-700">{error}</p>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="avanzamento" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Avanzamento
              </TabsTrigger>
              <TabsTrigger value="boq" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                BOQ
              </TabsTrigger>
              <TabsTrigger value="bobine" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Bobine
              </TabsTrigger>
              <TabsTrigger value="produttivita" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Produttività
              </TabsTrigger>
            </TabsList>

            {/* Tab Content: Avanzamento */}
            <TabsContent value="avanzamento" className="space-y-6">
              {reportProgress?.content ? (
                <>
                  {/* KPI Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card className="border-l-4 border-l-blue-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Metri Totali</CardTitle>
                        <Target className="h-4 w-4 text-blue-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.metri_totali?.toLocaleString() || 0}m
                        </div>
                        <p className="text-xs text-slate-500 mt-2">
                          {reportProgress.content.totale_cavi || 0} cavi totali
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-green-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Metri Posati</CardTitle>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.metri_posati?.toLocaleString() || 0}m
                        </div>
                        <Progress
                          value={reportProgress.content.percentuale_avanzamento || 0}
                          className="mt-2"
                        />
                        <p className="text-xs text-slate-500 mt-2">
                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-purple-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Media Giornaliera</CardTitle>
                        <TrendingUp className="h-4 w-4 text-purple-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.media_giornaliera?.toFixed(1) || 0}m
                        </div>
                        <p className="text-xs text-slate-500">metri/giorno</p>
                        <div className="flex items-center mt-2">
                          <Activity className="h-3 w-3 text-purple-500 mr-1" />
                          <span className="text-xs text-purple-600">
                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-orange-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Completamento</CardTitle>
                        <Clock className="h-4 w-4 text-orange-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.data_completamento || 'N/A'}
                        </div>
                        <p className="text-xs text-slate-500">stima completamento</p>
                        <div className="flex items-center mt-2">
                          <Calendar className="h-3 w-3 text-orange-500 mr-1" />
                          <span className="text-xs text-orange-600">
                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Posa Recente */}
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <div>
                          <CardTitle>Posa Recente</CardTitle>
                          <CardDescription>Ultimi 10 giorni di attività</CardDescription>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExportReport('progress', 'pdf')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          PDF
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={reportProgress.content.posa_recente || []}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="data" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="metri" fill="#3b82f6" name="Metri Posati" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Statistiche Cavi */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Stato Cavi</CardTitle>
                        <CardDescription>Distribuzione per stato di avanzamento</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Cavi Posati</span>
                            <Badge variant="secondary">
                              {reportProgress.content.cavi_posati || 0}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Cavi Rimanenti</span>
                            <Badge variant="outline">
                              {reportProgress.content.cavi_rimanenti || 0}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Percentuale Cavi</span>
                            <Badge variant="default">
                              {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Target className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato di avanzamento disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: BOQ */}
            <TabsContent value="boq" className="space-y-6">
              {reportBOQ?.content ? (
                <>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-semibold">Bill of Quantities</h3>
                      <p className="text-sm text-slate-600">Distinta materiali e fabbisogni</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport('boq', 'excel')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Excel
                    </Button>
                  </div>

                  {/* Cavi per Tipo */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Cavi per Tipologia</CardTitle>
                      <CardDescription>Fabbisogno cavi raggruppati per tipologia e formazione</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-left p-2">Formazione</th>
                              <th className="text-right p-2">Numero Cavi</th>
                              <th className="text-right p-2">Metri Teorici</th>
                              <th className="text-right p-2">Metri Reali</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportBOQ.content.cavi_per_tipo?.map((item: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{item.tipologia}</td>
                                <td className="p-2">{item.formazione}</td>
                                <td className="p-2 text-right">{item.num_cavi}</td>
                                <td className="p-2 text-right">{item.metri_teorici?.toLocaleString()}</td>
                                <td className="p-2 text-right">{item.metri_reali?.toLocaleString()}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Bobine per Tipo */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Bobine Disponibili</CardTitle>
                      <CardDescription>Inventario bobine per tipologia</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-left p-2">Formazione</th>
                              <th className="text-right p-2">Numero Bobine</th>
                              <th className="text-right p-2">Metri Disponibili</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{item.tipologia}</td>
                                <td className="p-2">{item.formazione}</td>
                                <td className="p-2 text-right">{item.num_bobine}</td>
                                <td className="p-2 text-right">
                                  <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>
                                    {item.metri_disponibili?.toLocaleString()}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato BOQ disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Bobine */}
            <TabsContent value="bobine" className="space-y-6">
              {reportUtilizzoBobine?.content ? (
                <>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-semibold">Utilizzo Bobine</h3>
                      <p className="text-sm text-slate-600">Stato e utilizzo delle bobine</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport('utilizzo-bobine', 'excel')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Excel
                    </Button>
                  </div>

                  {/* Statistiche Bobine */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Totale Bobine</CardTitle>
                        <Package className="h-4 w-4 text-blue-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.totale_bobine || 0}
                        </div>
                        <p className="text-xs text-slate-500">bobine nel cantiere</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Bobine Attive</CardTitle>
                        <Activity className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.bobine?.filter((b: any) =>
                            b.stato === 'In uso' || b.stato === 'Disponibile'
                          ).length || 0}
                        </div>
                        <p className="text-xs text-slate-500">disponibili/in uso</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Utilizzo Medio</CardTitle>
                        <TrendingUp className="h-4 w-4 text-purple-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.bobine?.length > 0 ?
                            (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>
                              acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length
                            ).toFixed(1) : 0}%
                        </div>
                        <p className="text-xs text-slate-500">utilizzo medio</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Lista Bobine */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Dettaglio Bobine</CardTitle>
                      <CardDescription>Stato dettagliato di tutte le bobine</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Codice</th>
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-right p-2">Metri Totali</th>
                              <th className="text-right p-2">Metri Utilizzati</th>
                              <th className="text-right p-2">Metri Residui</th>
                              <th className="text-right p-2">Utilizzo %</th>
                              <th className="text-left p-2">Stato</th>
                              <th className="text-right p-2">Cavi</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{bobina.codice}</td>
                                <td className="p-2">{bobina.tipologia}</td>
                                <td className="p-2 text-right">{bobina.metri_totali?.toLocaleString()}</td>
                                <td className="p-2 text-right">{bobina.metri_utilizzati?.toLocaleString()}</td>
                                <td className="p-2 text-right">{bobina.metri_residui?.toLocaleString()}</td>
                                <td className="p-2 text-right">
                                  <div className="flex items-center gap-2">
                                    <span>{bobina.percentuale_utilizzo?.toFixed(1)}%</span>
                                    <Progress
                                      value={Math.min(bobina.percentuale_utilizzo || 0, 100)}
                                      className="w-16 h-2"
                                    />
                                  </div>
                                </td>
                                <td className="p-2">
                                  <Badge
                                    variant={
                                      bobina.stato === 'Disponibile' ? 'default' :
                                      bobina.stato === 'In uso' ? 'secondary' :
                                      bobina.stato === 'Terminata' ? 'outline' :
                                      bobina.stato === 'Over' ? 'destructive' : 'outline'
                                    }
                                  >
                                    {bobina.stato}
                                  </Badge>
                                </td>
                                <td className="p-2 text-right">{bobina.totale_cavi_associati || 0}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato bobine disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Produttività */}
            <TabsContent value="produttivita" className="space-y-6">
              <div className="text-center py-12 text-slate-500">
                <Zap className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                <h3 className="text-lg font-semibold mb-2">Produttività</h3>
                <p>Funzionalità in fase di sviluppo</p>
                <p className="text-sm mt-2">
                  Includerà calcoli IAP, statistiche team e analisi performance
                </p>
              </div>
            </TabsContent>

          </Tabs>
        )}

      </div>
    </div>
  )
}
