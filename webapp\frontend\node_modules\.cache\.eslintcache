[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "17", "C:\\CMS\\webapp\\frontend\\src\\config.js": "18", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "19", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "26", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "27", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "28", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "29", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "30", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "31", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "32", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "33", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "34", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "39", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "40", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "46", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "58", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "59", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "60", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "61", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "62", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "67", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "70", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "72", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "73", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "74", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "84", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "85", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "86", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "91", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js": "97", "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js": "100", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js": "103", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js": "107", "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js": "108", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js": "109", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CollegamentiDialog.js": "110", "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\ProductivityMain.js": "111", "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\WorkLogForm.js": "112", "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\Dashboard.js": "113", "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\WorkLogsList.js": "114", "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\EstimationTool.js": "115", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPage.jsx": "116"}, {"size": 557, "mtime": 1746952718482, "results": "117", "hashOfConfig": "118"}, {"size": 3563, "mtime": 1750013079249, "results": "119", "hashOfConfig": "118"}, {"size": 996, "mtime": 1746970152489, "results": "120", "hashOfConfig": "118"}, {"size": 11085, "mtime": 1749414725518, "results": "121", "hashOfConfig": "118"}, {"size": 21191, "mtime": 1748751093271, "results": "122", "hashOfConfig": "118"}, {"size": 4903, "mtime": 1750140672956, "results": "123", "hashOfConfig": "118"}, {"size": 2216, "mtime": 1746640055487, "results": "124", "hashOfConfig": "118"}, {"size": 7394, "mtime": 1748034003517, "results": "125", "hashOfConfig": "118"}, {"size": 7347, "mtime": 1749615166316, "results": "126", "hashOfConfig": "118"}, {"size": 16193, "mtime": 1750351299924, "results": "127", "hashOfConfig": "118"}, {"size": 2455, "mtime": 1749188313610, "results": "128", "hashOfConfig": "118"}, {"size": 2050, "mtime": 1746647945415, "results": "129", "hashOfConfig": "118"}, {"size": 14255, "mtime": 1749366744543, "results": "130", "hashOfConfig": "118"}, {"size": 3028, "mtime": 1748816305304, "results": "131", "hashOfConfig": "118"}, {"size": 1630, "mtime": 1746336079554, "results": "132", "hashOfConfig": "118"}, {"size": 1909, "mtime": 1748722592098, "results": "133", "hashOfConfig": "118"}, {"size": 82534, "mtime": 1750096319495, "results": "134", "hashOfConfig": "118"}, {"size": 324, "mtime": 1749501001043, "results": "135", "hashOfConfig": "118"}, {"size": 2210, "mtime": 1747432283057, "results": "136", "hashOfConfig": "118"}, {"size": 53872, "mtime": 1750097247460, "results": "137", "hashOfConfig": "118"}, {"size": 3337, "mtime": 1748816346924, "results": "138", "hashOfConfig": "118"}, {"size": 2958, "mtime": 1748816316425, "results": "139", "hashOfConfig": "118"}, {"size": 3507, "mtime": 1748816326922, "results": "140", "hashOfConfig": "118"}, {"size": 3340, "mtime": 1748816336281, "results": "141", "hashOfConfig": "118"}, {"size": 6097, "mtime": 1749534549483, "results": "142", "hashOfConfig": "118"}, {"size": 5880, "mtime": 1748121404574, "results": "143", "hashOfConfig": "118"}, {"size": 3889, "mtime": 1748664890350, "results": "144", "hashOfConfig": "118"}, {"size": 4720, "mtime": 1746771178920, "results": "145", "hashOfConfig": "118"}, {"size": 7681, "mtime": 1749184406942, "results": "146", "hashOfConfig": "118"}, {"size": 10819, "mtime": 1749184481438, "results": "147", "hashOfConfig": "118"}, {"size": 6259, "mtime": 1746965906057, "results": "148", "hashOfConfig": "118"}, {"size": 4215, "mtime": 1746278746358, "results": "149", "hashOfConfig": "118"}, {"size": 1411, "mtime": 1750351314194, "results": "150", "hashOfConfig": "118"}, {"size": 14270, "mtime": 1748371983481, "results": "151", "hashOfConfig": "118"}, {"size": 2752, "mtime": 1747022186740, "results": "152", "hashOfConfig": "118"}, {"size": 1072, "mtime": 1746637929350, "results": "153", "hashOfConfig": "118"}, {"size": 6745, "mtime": 1747545492454, "results": "154", "hashOfConfig": "118"}, {"size": 539, "mtime": 1749491416504, "results": "155", "hashOfConfig": "118"}, {"size": 43883, "mtime": 1749161040576, "results": "156", "hashOfConfig": "118"}, {"size": 1947, "mtime": 1748120984640, "results": "157", "hashOfConfig": "118"}, {"size": 55218, "mtime": 1749676708816, "results": "158", "hashOfConfig": "118"}, {"size": 13911, "mtime": 1749069212408, "results": "159", "hashOfConfig": "118"}, {"size": 20923, "mtime": 1749709738306, "results": "160", "hashOfConfig": "118"}, {"size": 11835, "mtime": 1748920731807, "results": "161", "hashOfConfig": "118"}, {"size": 2211, "mtime": 1748686293878, "results": "162", "hashOfConfig": "118"}, {"size": 9215, "mtime": 1749162481509, "results": "163", "hashOfConfig": "118"}, {"size": 10993, "mtime": 1747154871546, "results": "164", "hashOfConfig": "118"}, {"size": 12217, "mtime": 1749161883257, "results": "165", "hashOfConfig": "118"}, {"size": 20081, "mtime": 1749162690470, "results": "166", "hashOfConfig": "118"}, {"size": 7032, "mtime": 1748069273238, "results": "167", "hashOfConfig": "118"}, {"size": 8589, "mtime": 1748207111023, "results": "168", "hashOfConfig": "118"}, {"size": 13653, "mtime": 1749367215461, "results": "169", "hashOfConfig": "118"}, {"size": 12817, "mtime": 1749183241975, "results": "170", "hashOfConfig": "118"}, {"size": 36555, "mtime": 1747684003188, "results": "171", "hashOfConfig": "118"}, {"size": 9128, "mtime": 1749069292534, "results": "172", "hashOfConfig": "118"}, {"size": 20387, "mtime": 1748984521895, "results": "173", "hashOfConfig": "118"}, {"size": 522, "mtime": 1747022186711, "results": "174", "hashOfConfig": "118"}, {"size": 11907, "mtime": 1749189769410, "results": "175", "hashOfConfig": "118"}, {"size": 12203, "mtime": 1750099189466, "results": "176", "hashOfConfig": "118"}, {"size": 1703, "mtime": 1746972529152, "results": "177", "hashOfConfig": "118"}, {"size": 18402, "mtime": 1749156991134, "results": "178", "hashOfConfig": "118"}, {"size": 12050, "mtime": 1747547543421, "results": "179", "hashOfConfig": "118"}, {"size": 1686, "mtime": 1746946499500, "results": "180", "hashOfConfig": "118"}, {"size": 5145, "mtime": 1746914029633, "results": "181", "hashOfConfig": "118"}, {"size": 10253, "mtime": 1749156772006, "results": "182", "hashOfConfig": "118"}, {"size": 32925, "mtime": 1749707219614, "results": "183", "hashOfConfig": "118"}, {"size": 2574, "mtime": 1748920719208, "results": "184", "hashOfConfig": "118"}, {"size": 4094, "mtime": 1748161663641, "results": "185", "hashOfConfig": "118"}, {"size": 4717, "mtime": 1749142942884, "results": "186", "hashOfConfig": "118"}, {"size": 4346, "mtime": 1747491472989, "results": "187", "hashOfConfig": "118"}, {"size": 15647, "mtime": 1748899398456, "results": "188", "hashOfConfig": "118"}, {"size": 7659, "mtime": 1749366714525, "results": "189", "hashOfConfig": "118"}, {"size": 12341, "mtime": 1749366595552, "results": "190", "hashOfConfig": "118"}, {"size": 15764, "mtime": 1748877145346, "results": "191", "hashOfConfig": "118"}, {"size": 6899, "mtime": 1748877131332, "results": "192", "hashOfConfig": "118"}, {"size": 5536, "mtime": 1748670096009, "results": "193", "hashOfConfig": "118"}, {"size": 5457, "mtime": 1748666884369, "results": "194", "hashOfConfig": "118"}, {"size": 5605, "mtime": 1748666925194, "results": "195", "hashOfConfig": "118"}, {"size": 82038, "mtime": 1749413441723, "results": "196", "hashOfConfig": "118"}, {"size": 22794, "mtime": 1749490955320, "results": "197", "hashOfConfig": "118"}, {"size": 3708, "mtime": 1748705727900, "results": "198", "hashOfConfig": "118"}, {"size": 10270, "mtime": 1748724524628, "results": "199", "hashOfConfig": "118"}, {"size": 15055, "mtime": 1748755908778, "results": "200", "hashOfConfig": "118"}, {"size": 16415, "mtime": 1748755956687, "results": "201", "hashOfConfig": "118"}, {"size": 3434, "mtime": 1748755857115, "results": "202", "hashOfConfig": "118"}, {"size": 3483, "mtime": 1748755829302, "results": "203", "hashOfConfig": "118"}, {"size": 3508, "mtime": 1748755842942, "results": "204", "hashOfConfig": "118"}, {"size": 956, "mtime": 1748878396989, "results": "205", "hashOfConfig": "118"}, {"size": 26404, "mtime": 1750099258905, "results": "206", "hashOfConfig": "118"}, {"size": 14748, "mtime": 1750097093009, "results": "207", "hashOfConfig": "118"}, {"size": 3613, "mtime": 1748921268108, "results": "208", "hashOfConfig": "118"}, {"size": 1153, "mtime": 1748921279608, "results": "209", "hashOfConfig": "118"}, {"size": 6579, "mtime": 1748922219011, "results": "210", "hashOfConfig": "118"}, {"size": 8976, "mtime": 1748922249445, "results": "211", "hashOfConfig": "118"}, {"size": 29408, "mtime": 1749154958292, "results": "212", "hashOfConfig": "118"}, {"size": 24586, "mtime": 1749155015426, "results": "213", "hashOfConfig": "118"}, {"size": 11668, "mtime": 1749366480246, "results": "214", "hashOfConfig": "118"}, {"size": 3389, "mtime": 1749417624087, "results": "215", "hashOfConfig": "118"}, {"size": 15031, "mtime": 1749439918292, "results": "216", "hashOfConfig": "118"}, {"size": 15003, "mtime": 1749419448637, "results": "217", "hashOfConfig": "118"}, {"size": 14243, "mtime": 1749419412675, "results": "218", "hashOfConfig": "118"}, {"size": 4833, "mtime": 1749487592760, "results": "219", "hashOfConfig": "118"}, {"size": 14380, "mtime": 1749489582520, "results": "220", "hashOfConfig": "118"}, {"size": 37827, "mtime": 1750097646333, "results": "221", "hashOfConfig": "118"}, {"size": 7486, "mtime": 1749556154284, "results": "222", "hashOfConfig": "118"}, {"size": 11558, "mtime": 1749710555149, "results": "223", "hashOfConfig": "118"}, {"size": 41221, "mtime": 1749620791154, "results": "224", "hashOfConfig": "118"}, {"size": 10103, "mtime": 1749620669254, "results": "225", "hashOfConfig": "118"}, {"size": 34499, "mtime": 1750021763766, "results": "226", "hashOfConfig": "118"}, {"size": 8649, "mtime": 1749711144482, "results": "227", "hashOfConfig": "118"}, {"size": 10234, "mtime": 1750130802923, "results": "228", "hashOfConfig": "118"}, {"size": 18999, "mtime": 1750139760522, "results": "229", "hashOfConfig": "118"}, {"size": 24733, "mtime": 1750139483761, "results": "230", "hashOfConfig": "118"}, {"size": 26024, "mtime": 1750139646388, "results": "231", "hashOfConfig": "118"}, {"size": 22495, "mtime": 1750139844740, "results": "232", "hashOfConfig": "118"}, {"size": 9133, "mtime": 1748206853844, "results": "233", "hashOfConfig": "118"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["582"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["583", "584", "585", "586"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["599"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["600", "601", "602", "603", "604", "605", "606", "607", "608", "609"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", ["610"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["626"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["627", "628", "629", "630", "631", "632", "633", "634"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["668"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["669", "670", "671", "672"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["673", "674"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["675", "676"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["677", "678", "679", "680"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["681", "682"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["683", "684"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["685", "686"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["687", "688"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["731", "732", "733", "734"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["744", "745"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["757"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["780"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["781"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["782"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["794", "795", "796", "797"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["812", "813"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["814"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["815", "816", "817", "818", "819"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["837", "838", "839", "840", "841"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["842"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["843", "844", "845"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["846", "847"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["848", "849"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["850"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["851"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["852"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["853", "854", "855", "856", "857", "858"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["859", "860"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["861", "862", "863", "864", "865", "866", "867", "868", "869"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["870", "871", "872", "873", "874", "875", "876", "877", "878"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["879", "880", "881", "882", "883", "884"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["885", "886", "887", "888", "889", "890"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["891", "892", "893", "894"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js", ["895", "896", "897", "898"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js", ["899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js", ["923"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js", ["924", "925", "926", "927", "928"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js", ["929"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js", ["930", "931", "932", "933", "934"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js", ["935", "936", "937"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js", ["938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js", ["949", "950", "951", "952", "953", "954", "955", "956", "957"], ["958"], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js", ["959", "960"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js", ["961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js", ["972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CollegamentiDialog.js", ["992"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\ProductivityMain.js", ["993", "994", "995", "996"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\WorkLogForm.js", ["997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\Dashboard.js", ["1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\WorkLogsList.js", ["1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\productivity\\EstimationTool.js", ["1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPage.jsx", ["1033", "1034", "1035", "1036", "1037", "1038"], [], {"ruleId": "1039", "severity": 1, "message": "1040", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 14}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 78, "column": 11, "nodeType": "1045", "messageId": "1046", "endLine": 78, "endColumn": 115}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 80, "column": 11, "nodeType": "1045", "messageId": "1046", "endLine": 80, "endColumn": 107}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 86, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 86, "endColumn": 105}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 89, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 89, "endColumn": 41}, {"ruleId": "1039", "severity": 1, "message": "1047", "line": 13, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1048", "line": 20, "column": 25, "nodeType": "1041", "messageId": "1042", "endLine": 20, "endColumn": 34}, {"ruleId": "1039", "severity": 1, "message": "1049", "line": 21, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 35}, {"ruleId": "1039", "severity": 1, "message": "1050", "line": 22, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 22, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1051", "line": 23, "column": 18, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 28}, {"ruleId": "1039", "severity": 1, "message": "1052", "line": 40, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 40, "endColumn": 35}, {"ruleId": "1039", "severity": 1, "message": "1053", "line": 40, "column": 37, "nodeType": "1041", "messageId": "1042", "endLine": 40, "endColumn": 62}, {"ruleId": "1039", "severity": 1, "message": "1054", "line": 57, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 57, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1055", "line": 58, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 58, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1056", "line": 59, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 59, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1057", "line": 60, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 60, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1058", "line": 69, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 69, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1059", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1060", "line": 2, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 31}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 2, "column": 33, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 37}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 2, "column": 39, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 50}, {"ruleId": "1039", "severity": 1, "message": "1063", "line": 2, "column": 52, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 66}, {"ruleId": "1039", "severity": 1, "message": "1047", "line": 2, "column": 68, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 74}, {"ruleId": "1039", "severity": 1, "message": "1048", "line": 5, "column": 25, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 34}, {"ruleId": "1039", "severity": 1, "message": "1049", "line": 6, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 6, "endColumn": 35}, {"ruleId": "1039", "severity": 1, "message": "1050", "line": 7, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 7, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1051", "line": 8, "column": 18, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 28}, {"ruleId": "1039", "severity": 1, "message": "1064", "line": 43, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 43, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1065", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1040", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1060", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1067", "line": 15, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1068", "line": 16, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1069", "line": 17, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1070", "line": 18, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 18, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1071", "line": 19, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 19, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1072", "line": 20, "column": 14, "nodeType": "1041", "messageId": "1042", "endLine": 20, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1073", "line": 25, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1074", "line": 28, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 28, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1075", "line": 48, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 48, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1076", "line": 53, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 53, "endColumn": 20}, {"ruleId": "1039", "severity": 1, "message": "1077", "line": 11, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 7, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 7, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1081", "line": 12, "column": 14, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1067", "line": 13, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1082", "line": 17, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1074", "line": 21, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1083", "line": 26, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1084", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1085", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1086", "line": 20, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 20, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1087", "line": 21, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1088", "line": 22, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 22, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1089", "line": 23, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1090", "line": 26, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1091", "line": 30, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1092", "line": 32, "column": 14, "nodeType": "1041", "messageId": "1042", "endLine": 32, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1093", "line": 33, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1094", "line": 34, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1095", "line": 35, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 35, "endColumn": 51}, {"ruleId": "1039", "severity": 1, "message": "1096", "line": 42, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 42, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1097", "line": 53, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 53, "endColumn": 24}, {"ruleId": "1039", "severity": 1, "message": "1098", "line": 54, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 54, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1074", "line": 67, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 67, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1099", "line": 68, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 32}, {"ruleId": "1039", "severity": 1, "message": "1052", "line": 68, "column": 34, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 58}, {"ruleId": "1039", "severity": 1, "message": "1100", "line": 68, "column": 60, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 82}, {"ruleId": "1039", "severity": 1, "message": "1053", "line": 68, "column": 84, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 109}, {"ruleId": "1039", "severity": 1, "message": "1101", "line": 68, "column": 111, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 133}, {"ruleId": "1039", "severity": 1, "message": "1102", "line": 68, "column": 135, "nodeType": "1041", "messageId": "1042", "endLine": 68, "endColumn": 160}, {"ruleId": "1039", "severity": 1, "message": "1103", "line": 69, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 69, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1083", "line": 71, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 71, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1104", "line": 291, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 291, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1105", "line": 299, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 299, "endColumn": 28}, {"ruleId": "1039", "severity": 1, "message": "1106", "line": 300, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 300, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1107", "line": 300, "column": 25, "nodeType": "1041", "messageId": "1042", "endLine": 300, "endColumn": 41}, {"ruleId": "1108", "severity": 1, "message": "1109", "line": 662, "column": 6, "nodeType": "1110", "endLine": 662, "endColumn": 15, "suggestions": "1111"}, {"ruleId": "1039", "severity": 1, "message": "1112", "line": 874, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 874, "endColumn": 33}, {"ruleId": "1108", "severity": 1, "message": "1113", "line": 178, "column": 6, "nodeType": "1110", "endLine": 178, "endColumn": 38, "suggestions": "1114"}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1083", "line": 26, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1115", "line": 48, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 48, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1115", "line": 37, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 37, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1115", "line": 52, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 52, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1083", "line": 26, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1115", "line": 48, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 48, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1074", "line": 24, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 26}, {"ruleId": "1108", "severity": 1, "message": "1116", "line": 53, "column": 6, "nodeType": "1110", "endLine": 53, "endColumn": 18, "suggestions": "1117"}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1120", "line": 28, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 28, "endColumn": 18}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1040", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1121", "line": 23, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1122", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 29, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 29, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1123", "line": 30, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1124", "line": 31, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 31, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1125", "line": 32, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 32, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1126", "line": 33, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1127", "line": 34, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1128", "line": 35, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 35, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1070", "line": 39, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 39, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1068", "line": 43, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 43, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1129", "line": 44, "column": 14, "nodeType": "1041", "messageId": "1042", "endLine": 44, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1130", "line": 50, "column": 69, "nodeType": "1041", "messageId": "1042", "endLine": 50, "endColumn": 76}, {"ruleId": "1039", "severity": 1, "message": "1131", "line": 79, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 79, "endColumn": 26}, {"ruleId": "1108", "severity": 1, "message": "1132", "line": 161, "column": 6, "nodeType": "1110", "endLine": 161, "endColumn": 8, "suggestions": "1133"}, {"ruleId": "1039", "severity": 1, "message": "1134", "line": 675, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 675, "endColumn": 26}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 260, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 264, "endColumn": 11}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 274, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 274, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 278, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 278, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 333, "column": 11, "nodeType": "1045", "messageId": "1046", "endLine": 338, "endColumn": 13}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 435, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 439, "endColumn": 11}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 451, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 451, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 653, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 653, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 662, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 662, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 666, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 666, "endColumn": 54}, {"ruleId": "1039", "severity": 1, "message": "1135", "line": 740, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 740, "endColumn": 22}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 760, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 764, "endColumn": 11}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 779, "column": 11, "nodeType": "1045", "messageId": "1046", "endLine": 783, "endColumn": 13}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 786, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 789, "endColumn": 11}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 795, "column": 11, "nodeType": "1045", "messageId": "1046", "endLine": 799, "endColumn": 13}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 802, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 805, "endColumn": 11}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 870, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 874, "endColumn": 11}, {"ruleId": "1136", "severity": 1, "message": "1137", "line": 940, "column": 3, "nodeType": "1138", "messageId": "1139", "endLine": 940, "endColumn": 29}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 1237, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 1237, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 1267, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 1267, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 1320, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 1320, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 1367, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 1367, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 1421, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 1425, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1140", "line": 6, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 6, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1141", "line": 20, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 20, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1142", "line": 205, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 205, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 2, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1143", "line": 2, "column": 64, "nodeType": "1041", "messageId": "1042", "endLine": 2, "endColumn": 70}, {"ruleId": "1039", "severity": 1, "message": "1094", "line": 4, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1144", "line": 5, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1145", "line": 6, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 6, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1096", "line": 7, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 7, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1146", "line": 8, "column": 16, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1147", "line": 14, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 20}, {"ruleId": "1039", "severity": 1, "message": "1148", "line": 121, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 121, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1149", "line": 3, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 3, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1150", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 6}, {"ruleId": "1039", "severity": 1, "message": "1151", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1152", "line": 6, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 6, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1153", "line": 7, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 7, "endColumn": 6}, {"ruleId": "1039", "severity": 1, "message": "1154", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1155", "line": 36, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 36, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1156", "line": 50, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 50, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1157", "line": 64, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 64, "endColumn": 20}, {"ruleId": "1039", "severity": 1, "message": "1158", "line": 88, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 88, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1159", "line": 104, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 104, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1160", "line": 3, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 3, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1152", "line": 3, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 3, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1153", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 6}, {"ruleId": "1039", "severity": 1, "message": "1161", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1162", "line": 6, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 6, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1163", "line": 7, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 7, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1164", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1154", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1165", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1149", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1150", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 6}, {"ruleId": "1039", "severity": 1, "message": "1151", "line": 13, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1166", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1167", "line": 15, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1160", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1084", "line": 18, "column": 40, "nodeType": "1041", "messageId": "1042", "endLine": 18, "endColumn": 44}, {"ruleId": "1039", "severity": 1, "message": "1168", "line": 47, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 47, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1169", "line": 64, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 64, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1170", "line": 71, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 71, "endColumn": 20}, {"ruleId": "1039", "severity": 1, "message": "1158", "line": 79, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 79, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1159", "line": 95, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 95, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1171", "line": 272, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 272, "endColumn": 37}, {"ruleId": "1039", "severity": 1, "message": "1172", "line": 273, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 273, "endColumn": 36}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 3, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 3, "endColumn": 8}, {"ruleId": "1108", "severity": 1, "message": "1173", "line": 60, "column": 6, "nodeType": "1110", "endLine": 60, "endColumn": 34, "suggestions": "1174"}, {"ruleId": "1039", "severity": 1, "message": "1175", "line": 25, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1176", "line": 33, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1177", "line": 34, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1178", "line": 35, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 35, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1179", "line": 36, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 36, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1180", "line": 37, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 37, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1181", "line": 41, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 41, "endColumn": 20}, {"ruleId": "1039", "severity": 1, "message": "1182", "line": 43, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 43, "endColumn": 34}, {"ruleId": "1039", "severity": 1, "message": "1183", "line": 69, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 69, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1184", "line": 69, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 69, "endColumn": 29}, {"ruleId": "1108", "severity": 1, "message": "1185", "line": 88, "column": 6, "nodeType": "1110", "endLine": 88, "endColumn": 18, "suggestions": "1186"}, {"ruleId": "1108", "severity": 1, "message": "1187", "line": 448, "column": 6, "nodeType": "1110", "endLine": 448, "endColumn": 28, "suggestions": "1188"}, {"ruleId": "1039", "severity": 1, "message": "1065", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1141", "line": 21, "column": 20, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1142", "line": 100, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 100, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1189", "line": 119, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 119, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1190", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1191", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1192", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1193", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1194", "line": 13, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1195", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1196", "line": 15, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1197", "line": 36, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 36, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1198", "line": 37, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 37, "endColumn": 20}, {"ruleId": "1108", "severity": 1, "message": "1199", "line": 46, "column": 6, "nodeType": "1110", "endLine": 46, "endColumn": 18, "suggestions": "1200"}, {"ruleId": "1039", "severity": 1, "message": "1201", "line": 265, "column": 23, "nodeType": "1041", "messageId": "1042", "endLine": 265, "endColumn": 44}, {"ruleId": "1039", "severity": 1, "message": "1202", "line": 266, "column": 23, "nodeType": "1041", "messageId": "1042", "endLine": 266, "endColumn": 42}, {"ruleId": "1039", "severity": 1, "message": "1201", "line": 381, "column": 21, "nodeType": "1041", "messageId": "1042", "endLine": 381, "endColumn": 42}, {"ruleId": "1039", "severity": 1, "message": "1202", "line": 382, "column": 21, "nodeType": "1041", "messageId": "1042", "endLine": 382, "endColumn": 40}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1203", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1136", "severity": 1, "message": "1204", "line": 127, "column": 3, "nodeType": "1138", "messageId": "1139", "endLine": 127, "endColumn": 19}, {"ruleId": "1136", "severity": 1, "message": "1205", "line": 149, "column": 3, "nodeType": "1138", "messageId": "1139", "endLine": 149, "endColumn": 27}, {"ruleId": "1136", "severity": 1, "message": "1206", "line": 302, "column": 3, "nodeType": "1138", "messageId": "1139", "endLine": 302, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1118", "line": 1, "column": 8, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1119", "line": 5, "column": 7, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1207", "line": 83, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 83, "endColumn": 21}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 109, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 109, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 123, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 123, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 127, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 127, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 212, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 212, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 226, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 226, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 230, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 230, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 271, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 271, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 280, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 280, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 284, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 284, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 320, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 320, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 324, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 324, "endColumn": 54}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 416, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 416, "endColumn": 163}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 425, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 425, "endColumn": 70}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 429, "column": 9, "nodeType": "1045", "messageId": "1046", "endLine": 429, "endColumn": 54}, {"ruleId": "1039", "severity": 1, "message": "1183", "line": 60, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 60, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1184", "line": 60, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 60, "endColumn": 29}, {"ruleId": "1108", "severity": 1, "message": "1199", "line": 90, "column": 6, "nodeType": "1110", "endLine": 90, "endColumn": 32, "suggestions": "1208"}, {"ruleId": "1039", "severity": 1, "message": "1209", "line": 378, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 378, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1210", "line": 478, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 478, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1211", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1089", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1088", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1087", "line": 19, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 19, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1090", "line": 14, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1212", "line": 43, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 43, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1213", "line": 25, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1214", "line": 33, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1215", "line": 3, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 3, "endColumn": 6}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1175", "line": 58, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 58, "endColumn": 27}, {"ruleId": "1108", "severity": 1, "message": "1216", "line": 140, "column": 6, "nodeType": "1110", "endLine": 140, "endColumn": 18, "suggestions": "1217"}, {"ruleId": "1108", "severity": 1, "message": "1218", "line": 145, "column": 6, "nodeType": "1110", "endLine": 145, "endColumn": 52, "suggestions": "1219"}, {"ruleId": "1108", "severity": 1, "message": "1220", "line": 150, "column": 6, "nodeType": "1110", "endLine": 150, "endColumn": 62, "suggestions": "1221"}, {"ruleId": "1108", "severity": 1, "message": "1222", "line": 155, "column": 6, "nodeType": "1110", "endLine": 155, "endColumn": 28, "suggestions": "1223"}, {"ruleId": "1108", "severity": 1, "message": "1224", "line": 164, "column": 6, "nodeType": "1110", "endLine": 164, "endColumn": 39, "suggestions": "1225"}, {"ruleId": "1039", "severity": 1, "message": "1226", "line": 39, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 39, "endColumn": 23}, {"ruleId": "1108", "severity": 1, "message": "1227", "line": 71, "column": 6, "nodeType": "1110", "endLine": 71, "endColumn": 18, "suggestions": "1228"}, {"ruleId": "1039", "severity": 1, "message": "1229", "line": 1, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 36}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1211", "line": 27, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 27, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1069", "line": 30, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1230", "line": 33, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1051", "line": 34, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1081", "line": 35, "column": 14, "nodeType": "1041", "messageId": "1042", "endLine": 35, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1211", "line": 27, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 27, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1231", "line": 28, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 28, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1232", "line": 29, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 29, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1233", "line": 30, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1069", "line": 34, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1234", "line": 37, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 37, "endColumn": 31}, {"ruleId": "1108", "severity": 1, "message": "1235", "line": 98, "column": 6, "nodeType": "1110", "endLine": 98, "endColumn": 24, "suggestions": "1236"}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1237", "line": 29, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 29, "endColumn": 15}, {"ruleId": "1108", "severity": 1, "message": "1238", "line": 87, "column": 6, "nodeType": "1110", "endLine": 87, "endColumn": 25, "suggestions": "1239"}, {"ruleId": "1108", "severity": 1, "message": "1240", "line": 94, "column": 6, "nodeType": "1110", "endLine": 94, "endColumn": 24, "suggestions": "1241"}, {"ruleId": "1039", "severity": 1, "message": "1242", "line": 152, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 152, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1078", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1084", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1243", "line": 196, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 196, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1244", "line": 233, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 233, "endColumn": 24}, {"ruleId": "1108", "severity": 1, "message": "1245", "line": 389, "column": 6, "nodeType": "1110", "endLine": 389, "endColumn": 58, "suggestions": "1246"}, {"ruleId": "1039", "severity": 1, "message": "1247", "line": 409, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 409, "endColumn": 28}, {"ruleId": "1039", "severity": 1, "message": "1086", "line": 15, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1087", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1088", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1089", "line": 18, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 18, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1192", "line": 21, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1248", "line": 28, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 28, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1181", "line": 33, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1249", "line": 78, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 78, "endColumn": 24}, {"ruleId": "1039", "severity": 1, "message": "1123", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1124", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1125", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1126", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1127", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1128", "line": 13, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1211", "line": 15, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1192", "line": 30, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1164", "line": 32, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 32, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1140", "line": 33, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1141", "line": 40, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 40, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1096", "line": 42, "column": 15, "nodeType": "1041", "messageId": "1042", "endLine": 42, "endColumn": 27}, {"ruleId": "1039", "severity": 1, "message": "1176", "line": 50, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 50, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1177", "line": 51, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 51, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1178", "line": 52, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 52, "endColumn": 22}, {"ruleId": "1039", "severity": 1, "message": "1179", "line": 53, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 53, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1180", "line": 54, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 54, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1250", "line": 55, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 55, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1251", "line": 56, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 56, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1252", "line": 57, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 57, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1181", "line": 58, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 58, "endColumn": 20}, {"ruleId": "1108", "severity": 1, "message": "1253", "line": 96, "column": 6, "nodeType": "1110", "endLine": 96, "endColumn": 32, "suggestions": "1254"}, {"ruleId": "1039", "severity": 1, "message": "1255", "line": 223, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 223, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1213", "line": 21, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 29}, {"ruleId": "1039", "severity": 1, "message": "1229", "line": 1, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 36}, {"ruleId": "1039", "severity": 1, "message": "1256", "line": 51, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 51, "endColumn": 30}, {"ruleId": "1039", "severity": 1, "message": "1257", "line": 52, "column": 29, "nodeType": "1041", "messageId": "1042", "endLine": 52, "endColumn": 49}, {"ruleId": "1039", "severity": 1, "message": "1258", "line": 242, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 242, "endColumn": 36}, {"ruleId": "1039", "severity": 1, "message": "1259", "line": 246, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 246, "endColumn": 38}, {"ruleId": "1039", "severity": 1, "message": "1260", "line": 75, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 75, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1261", "line": 123, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 123, "endColumn": 22}, {"ruleId": "1262", "severity": 1, "message": "1263", "line": 126, "column": 5, "nodeType": "1264", "messageId": "1265", "endLine": 201, "endColumn": 6}, {"ruleId": "1262", "severity": 1, "message": "1263", "line": 219, "column": 5, "nodeType": "1264", "messageId": "1265", "endLine": 279, "endColumn": 6}, {"ruleId": "1262", "severity": 1, "message": "1263", "line": 290, "column": 5, "nodeType": "1264", "messageId": "1265", "endLine": 336, "endColumn": 6}, {"ruleId": "1266", "severity": 1, "message": "1267", "line": 429, "column": 1, "nodeType": "1268", "endLine": 429, "endColumn": 47}, {"ruleId": "1269", "severity": 1, "message": "1270", "line": 146, "column": 25, "nodeType": "1271", "messageId": "1272", "endLine": 146, "endColumn": 26, "suggestions": "1273"}, {"ruleId": "1269", "severity": 1, "message": "1274", "line": 146, "column": 37, "nodeType": "1271", "messageId": "1272", "endLine": 146, "endColumn": 38, "suggestions": "1275"}, {"ruleId": "1269", "severity": 1, "message": "1276", "line": 146, "column": 39, "nodeType": "1271", "messageId": "1272", "endLine": 146, "endColumn": 40, "suggestions": "1277"}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 5, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 5, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1123", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1124", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1125", "line": 10, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 10, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1126", "line": 11, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 11, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1127", "line": 12, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 12, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1128", "line": 13, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 13, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 31, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 31, "endColumn": 10}, {"ruleId": "1108", "severity": 1, "message": "1278", "line": 64, "column": 6, "nodeType": "1110", "endLine": 64, "endColumn": 24, "suggestions": "1279"}, {"ruleId": "1039", "severity": 1, "message": "1084", "line": 8, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 8, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1190", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1191", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1192", "line": 18, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 18, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 21, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1049", "line": 30, "column": 19, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 35}, {"ruleId": "1039", "severity": 1, "message": "1280", "line": 343, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 343, "endColumn": 35}, {"ruleId": "1039", "severity": 1, "message": "1281", "line": 601, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 601, "endColumn": 28}, {"ruleId": "1039", "severity": 1, "message": "1282", "line": 611, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 611, "endColumn": 22}, {"ruleId": "1108", "severity": 1, "message": "1283", "line": 341, "column": 6, "nodeType": "1110", "endLine": 341, "endColumn": 88, "suggestions": "1284", "suppressions": "1285"}, {"ruleId": "1039", "severity": 1, "message": "1286", "line": 1, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 25}, {"ruleId": "1039", "severity": 1, "message": "1229", "line": 1, "column": 27, "nodeType": "1041", "messageId": "1042", "endLine": 1, "endColumn": 36}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 29, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 29, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 30, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1040", "line": 31, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 31, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1231", "line": 32, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 32, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1232", "line": 33, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 33, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1233", "line": 34, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1234", "line": 42, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 42, "endColumn": 31}, {"ruleId": "1039", "severity": 1, "message": "1287", "line": 100, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 100, "endColumn": 24}, {"ruleId": "1039", "severity": 1, "message": "1288", "line": 101, "column": 25, "nodeType": "1041", "messageId": "1042", "endLine": 101, "endColumn": 41}, {"ruleId": "1108", "severity": 1, "message": "1289", "line": 141, "column": 6, "nodeType": "1110", "endLine": 141, "endColumn": 16, "suggestions": "1290"}, {"ruleId": "1262", "severity": 1, "message": "1263", "line": 146, "column": 7, "nodeType": "1264", "messageId": "1265", "endLine": 165, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 21, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 21, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1164", "line": 22, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 22, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1086", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1087", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1088", "line": 26, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1089", "line": 27, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 27, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1231", "line": 38, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 38, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1232", "line": 39, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 39, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1233", "line": 40, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 40, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1291", "line": 41, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 41, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1292", "line": 47, "column": 18, "nodeType": "1041", "messageId": "1042", "endLine": 47, "endColumn": 33}, {"ruleId": "1039", "severity": 1, "message": "1293", "line": 50, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 50, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1090", "line": 51, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 51, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1234", "line": 52, "column": 17, "nodeType": "1041", "messageId": "1042", "endLine": 52, "endColumn": 31}, {"ruleId": "1039", "severity": 1, "message": "1294", "line": 53, "column": 13, "nodeType": "1041", "messageId": "1042", "endLine": 53, "endColumn": 23}, {"ruleId": "1039", "severity": 1, "message": "1295", "line": 54, "column": 12, "nodeType": "1041", "messageId": "1042", "endLine": 54, "endColumn": 21}, {"ruleId": "1039", "severity": 1, "message": "1296", "line": 55, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 55, "endColumn": 19}, {"ruleId": "1039", "severity": 1, "message": "1297", "line": 78, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 78, "endColumn": 17}, {"ruleId": "1108", "severity": 1, "message": "1298", "line": 86, "column": 6, "nodeType": "1110", "endLine": 86, "endColumn": 21, "suggestions": "1299"}, {"ruleId": "1039", "severity": 1, "message": "1300", "line": 442, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 442, "endColumn": 28}, {"ruleId": "1108", "severity": 1, "message": "1301", "line": 46, "column": 6, "nodeType": "1110", "endLine": 46, "endColumn": 21, "suggestions": "1302"}, {"ruleId": "1039", "severity": 1, "message": "1061", "line": 14, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 14, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1062", "line": 15, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 15, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1080", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1164", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1303", "line": 9, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 9, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1079", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1047", "line": 18, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 18, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1304", "line": 22, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 22, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1305", "line": 23, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1306", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1307", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1308", "line": 34, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 34, "endColumn": 14}, {"ruleId": "1108", "severity": 1, "message": "1216", "line": 99, "column": 6, "nodeType": "1110", "endLine": 99, "endColumn": 19, "suggestions": "1309"}, {"ruleId": "1039", "severity": 1, "message": "1310", "line": 17, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 17, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1088", "line": 23, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 9}, {"ruleId": "1039", "severity": 1, "message": "1089", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1086", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1087", "line": 26, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 26, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 31, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 31, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1311", "line": 66, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 66, "endColumn": 18}, {"ruleId": "1108", "severity": 1, "message": "1312", "line": 86, "column": 6, "nodeType": "1110", "endLine": 86, "endColumn": 36, "suggestions": "1313"}, {"ruleId": "1039", "severity": 1, "message": "1310", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 19, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 19, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1314", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1315", "line": 42, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 42, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1316", "line": 46, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 46, "endColumn": 8}, {"ruleId": "1039", "severity": 1, "message": "1317", "line": 47, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 47, "endColumn": 8}, {"ruleId": "1108", "severity": 1, "message": "1318", "line": 89, "column": 6, "nodeType": "1110", "endLine": 89, "endColumn": 51, "suggestions": "1319"}, {"ruleId": "1039", "severity": 1, "message": "1320", "line": 176, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 176, "endColumn": 24}, {"ruleId": "1039", "severity": 1, "message": "1321", "line": 185, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 185, "endColumn": 26}, {"ruleId": "1039", "severity": 1, "message": "1310", "line": 4, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 4, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1066", "line": 19, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 19, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1304", "line": 22, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 22, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1305", "line": 23, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 23, "endColumn": 7}, {"ruleId": "1039", "severity": 1, "message": "1306", "line": 24, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 24, "endColumn": 12}, {"ruleId": "1039", "severity": 1, "message": "1307", "line": 25, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 25, "endColumn": 14}, {"ruleId": "1039", "severity": 1, "message": "1322", "line": 29, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 29, "endColumn": 13}, {"ruleId": "1039", "severity": 1, "message": "1323", "line": 30, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 30, "endColumn": 11}, {"ruleId": "1039", "severity": 1, "message": "1324", "line": 36, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 36, "endColumn": 10}, {"ruleId": "1039", "severity": 1, "message": "1325", "line": 188, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 188, "endColumn": 33}, {"ruleId": "1039", "severity": 1, "message": "1196", "line": 16, "column": 3, "nodeType": "1041", "messageId": "1042", "endLine": 16, "endColumn": 16}, {"ruleId": "1039", "severity": 1, "message": "1103", "line": 47, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 47, "endColumn": 17}, {"ruleId": "1039", "severity": 1, "message": "1326", "line": 48, "column": 11, "nodeType": "1041", "messageId": "1042", "endLine": 48, "endColumn": 15}, {"ruleId": "1039", "severity": 1, "message": "1327", "line": 55, "column": 10, "nodeType": "1041", "messageId": "1042", "endLine": 55, "endColumn": 21}, {"ruleId": "1108", "severity": 1, "message": "1289", "line": 70, "column": 6, "nodeType": "1110", "endLine": 70, "endColumn": 18, "suggestions": "1328"}, {"ruleId": "1039", "severity": 1, "message": "1329", "line": 151, "column": 9, "nodeType": "1041", "messageId": "1042", "endLine": 151, "endColumn": 30}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'TextField' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'Chip' is defined but never used.", "'LinearProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'parcoCaviService' is defined but never used.", "'CavoForm' is defined but never used.", "'openEliminaCavoDialog' is assigned a value but never used.", "'openModificaCavoDialog' is assigned a value but never used.", "'openAggiungiCavoDialog' is assigned a value but never used.", "'setOpenAggiungiCavoDialog' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1330"], "'handleCreateCommandError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array.", ["1331"], "'handleBackToCantieri' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1332"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1333"], "'renderBobineCards' is assigned a value but never used.", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "'Stack' is defined but never used.", "'CancelIcon' is defined but never used.", "'handleCancel' is assigned a value but never used.", "'Button' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'formatDate' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array.", ["1334"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1335"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1336"], "'handleBackToSelection' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1337"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'config' is defined but never used.", "Duplicate key 'aggiornaDatiPosa'.", "Duplicate key 'aggiornaDatiCollegamento'.", "Duplicate key 'aggiornaDatiCertificazione'.", "'sentData' is assigned a value but never used.", ["1338"], "'result' is assigned a value but never used.", "'hasMetri' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1339"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1340"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1341"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1342"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1343"], "'PeopleIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1344"], "'useEffect' is defined but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1345"], "'Autocomplete' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1346"], "React Hook useEffect has a missing dependency: 'loadResponsabiliDisponibili'. Either include it or remove the dependency array.", ["1347"], "'handleBack' is assigned a value but never used.", "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1348"], "'getSearchTermsCount' is assigned a value but never used.", "'CloseIcon' is defined but never used.", "'getBobinaNumber' is assigned a value but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1349"], "'bobina' is assigned a value but never used.", "'showValidationDialog' is assigned a value but never used.", "'setValidationLoading' is assigned a value but never used.", "'handleValidationDialogClose' is assigned a value but never used.", "'handleValidationDialogProceed' is assigned a value but never used.", "'getSeverityColor' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["1350", "1351"], "Unnecessary escape character: \\(.", ["1352", "1353"], "Unnecessary escape character: \\).", ["1354", "1355"], "React Hook useEffect has a missing dependency: 'loadResponsabili'. Either include it or remove the dependency array.", ["1356"], "'loadComandePerResponsabili' is assigned a value but never used.", "'getTipoComandaLabel' is assigned a value but never used.", "'getStatoColor' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadResponsabili', 'searchingComanda', and 'setSearchParams'. Either include them or remove the dependency array.", ["1357"], ["1358"], "'useState' is defined but never used.", "'tipologieTotal' is assigned a value but never used.", "'setTipologiePage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["1359"], "'Collapse' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'BuildIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'isTablet' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobineDisponibili' and 'loadCaviComanda'. Either include them or remove the dependency array.", ["1360"], "'isBobinaCompatibile' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCaviComanda'. Either include it or remove the dependency array.", ["1361"], "'CardHeader' is defined but never used.", "'Stepper' is defined but never used.", "'Step' is defined but never used.", "'StepLabel' is defined but never used.", "'StepContent' is defined but never used.", "'CheckCircle' is defined but never used.", ["1362"], "'Container' is defined but never used.", "'workLogs' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["1363"], "'Badge' is defined but never used.", "'Visibility' is defined but never used.", "'Timer' is defined but never used.", "'Group' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadWorkLogs'. Either include it or remove the dependency array.", ["1364"], "'getActivityIcon' is assigned a value but never used.", "'getConditionColor' is assigned a value but never used.", "'TrendingUp' is defined but never used.", "'Schedule' is defined but never used.", "'Warning' is defined but never used.", "'getCorrectionFactorColor' is assigned a value but never used.", "'user' is assigned a value but never used.", "'formLoading' is assigned a value but never used.", ["1365"], "'handleStrumentoSubmit' is assigned a value but never used.", {"desc": "1366", "fix": "1367"}, {"desc": "1368", "fix": "1369"}, {"desc": "1370", "fix": "1371"}, {"desc": "1372", "fix": "1373"}, {"desc": "1374", "fix": "1375"}, {"desc": "1376", "fix": "1377"}, {"desc": "1378", "fix": "1379"}, {"desc": "1380", "fix": "1381"}, {"desc": "1382", "fix": "1383"}, {"desc": "1384", "fix": "1385"}, {"desc": "1386", "fix": "1387"}, {"desc": "1388", "fix": "1389"}, {"desc": "1390", "fix": "1391"}, {"desc": "1392", "fix": "1393"}, {"desc": "1394", "fix": "1395"}, {"desc": "1396", "fix": "1397"}, {"desc": "1398", "fix": "1399"}, {"desc": "1400", "fix": "1401"}, {"desc": "1402", "fix": "1403"}, {"desc": "1404", "fix": "1405"}, {"messageId": "1406", "fix": "1407", "desc": "1408"}, {"messageId": "1409", "fix": "1410", "desc": "1411"}, {"messageId": "1406", "fix": "1412", "desc": "1408"}, {"messageId": "1409", "fix": "1413", "desc": "1411"}, {"messageId": "1406", "fix": "1414", "desc": "1408"}, {"messageId": "1409", "fix": "1415", "desc": "1411"}, {"desc": "1416", "fix": "1417"}, {"desc": "1418", "fix": "1419"}, {"kind": "1420", "justification": "1421"}, {"desc": "1422", "fix": "1423"}, {"desc": "1424", "fix": "1425"}, {"desc": "1426", "fix": "1427"}, {"desc": "1428", "fix": "1429"}, {"desc": "1430", "fix": "1431"}, {"desc": "1432", "fix": "1433"}, {"desc": "1434", "fix": "1435"}, "Update the dependencies array to be: [calculateStatistics, caviAttivi, caviSpare, error, filters, user]", {"range": "1436", "text": "1437"}, "Update the dependencies array to be: [cantiereId, loadStoricoBobine, selectedReportType]", {"range": "1438", "text": "1439"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1440", "text": "1441"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1442", "text": "1443"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi, loadWeatherData]", {"range": "1444", "text": "1445"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1446", "text": "1447"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1448", "text": "1449"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1450", "text": "1451"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1452", "text": "1453"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1454", "text": "1455"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1456", "text": "1457"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1458", "text": "1459"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1460", "text": "1461"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1462", "text": "1463"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1464", "text": "1465"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1466", "text": "1467"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1468", "text": "1469"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabiliDisponibili]", {"range": "1470", "text": "1471"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1472", "text": "1473"}, "Update the dependencies array to be: [open, cavoPreselezionato, loadBobine]", {"range": "1474", "text": "1475"}, "removeEscape", {"range": "1476", "text": "1421"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1477", "text": "1478"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1479", "text": "1421"}, {"range": "1480", "text": "1478"}, {"range": "1481", "text": "1421"}, {"range": "1482", "text": "1478"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabili]", {"range": "1483", "text": "1484"}, "Update the dependencies array to be: [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", {"range": "1485", "text": "1486"}, "directive", "", "Update the dependencies array to be: [loadData, tabValue]", {"range": "1487", "text": "1488"}, "Update the dependencies array to be: [open, comanda, loadCaviComanda, loadBobineDisponibili]", {"range": "1489", "text": "1490"}, "Update the dependencies array to be: [open, comanda, loadCaviComanda]", {"range": "1491", "text": "1492"}, "Update the dependencies array to be: [initialData, loadInitialData]", {"range": "1493", "text": "1494"}, "Update the dependencies array to be: [currentCantiereId, dateRange, loadDashboardData]", {"range": "1495", "text": "1496"}, "Update the dependencies array to be: [currentCantiereId, filters, loadWorkLogs, pagination.page]", {"range": "1497", "text": "1498"}, "Update the dependencies array to be: [cantiereId, loadData]", {"range": "1499", "text": "1500"}, [25892, 25901], "[calculateStatistics, caviAttivi, caviSpare, error, filters, user]", [5510, 5542], "[cantiere<PERSON>d, loadStoricoBobine, selectedReportType]", [1559, 1571], "[cantiereId, selectCantiere]", [5793, 5795], "[handleOptionSelect, initialOption, loadBobine]", [1809, 1837], "[certificazione, cantiereId, loadCavi, loadWeatherData]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [2734, 2760], "[open, bobina, cantiereId, loadCavi]", [3803, 3815], "[cantiereId, loadInitialData]", [3900, 3946], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [4030, 4086], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [4192, 4214], "[calculateStatistics, cavi, certificazioni]", [4436, 4469], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1672, 1684], "[cantiereId, loadComande, loadStatistiche]", [2516, 2534], "[certificazioneId, loadProve]", [2155, 2174], "[loadCaviDisponibili, open, tipoComanda]", [2338, 2356], "[open, cantiereId, loadResponsabiliDisponibili]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", [2440, 2466], "[open, cavoPreselezionato, loadBobine]", [4732, 4733], [4732, 4732], "\\", [4744, 4745], [4744, 4744], [4746, 4747], [4746, 4746], [1518, 1536], "[open, cantiereId, loadResponsabili]", [12534, 12616], "[searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", [3615, 3625], "[loadData, tabValue]", [1924, 1939], "[open, comanda, loadCaviComanda, loadBobineDisponibili]", [892, 907], "[open, comanda, loadCaviComanda]", [2652, 2665], "[initialData, loadInitialData]", [2008, 2038], "[currentCantiereId, dateRange, loadDashboardData]", [1918, 1963], "[currentCantiereId, filters, loadWorkLogs, pagination.page]", [2160, 2172], "[cantiereId, loadData]"]